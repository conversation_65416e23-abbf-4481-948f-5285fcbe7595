const { db } = require('../db/database');
const { v4: uuidv4 } = require('uuid');

class Referral {
  /**
   * Generate unique referral code for user
   * @param {string} userId - User ID
   * @returns {Promise<string>} Generated referral code
   */
  static async generateReferralCode(userId) {
    try {
      // Generate a unique 8-character code
      const code = Math.random().toString(36).substring(2, 10).toUpperCase();
      
      // Check if code already exists
      const existing = await this.getReferralByCode(code);
      if (existing) {
        // Recursively generate new code if exists
        return this.generateReferralCode(userId);
      }

      // Create referral record
      const referralId = uuidv4();
      await new Promise((resolve, reject) => {
        db.run(
          `INSERT INTO referrals (id, referrer_id, referral_code) VALUES (?, ?, ?)`,
          [referralId, userId, code],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      // Update user's referral code
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE users SET referral_code = ? WHERE id = ?`,
          [code, userId],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      return code;
    } catch (error) {
      console.error('Error generating referral code:', error);
      throw error;
    }
  }

  /**
   * Get referral by code
   * @param {string} code - Referral code
   * @returns {Promise<Object|null>} Referral data
   */
  static getReferralByCode(code) {
    return new Promise((resolve, reject) => {
      db.get(
        `SELECT r.*, u.username as referrer_username 
         FROM referrals r 
         JOIN users u ON r.referrer_id = u.id 
         WHERE r.referral_code = ?`,
        [code],
        (err, row) => {
          if (err) reject(err);
          else resolve(row || null);
        }
      );
    });
  }

  /**
   * Get user's referral statistics
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Referral statistics
   */
  static async getUserReferralStats(userId) {
    try {
      // Get user's referral balance
      const user = await new Promise((resolve, reject) => {
        db.get(
          `SELECT referral_balance, referral_code FROM users WHERE id = ?`,
          [userId],
          (err, row) => {
            if (err) reject(err);
            else resolve(row);
          }
        );
      });

      // Get total clicks
      const clicksResult = await new Promise((resolve, reject) => {
        db.get(
          `SELECT COUNT(*) as total_clicks 
           FROM referral_clicks rc 
           JOIN referrals r ON rc.referral_code = r.referral_code 
           WHERE r.referrer_id = ?`,
          [userId],
          (err, row) => {
            if (err) reject(err);
            else resolve(row);
          }
        );
      });

      // Get pending referrals count
      const pendingResult = await new Promise((resolve, reject) => {
        db.get(
          `SELECT COUNT(*) as pending_count 
           FROM referrals 
           WHERE referrer_id = ? AND status = 'pending'`,
          [userId],
          (err, row) => {
            if (err) reject(err);
            else resolve(row);
          }
        );
      });

      // Get successful referrals count
      const successResult = await new Promise((resolve, reject) => {
        db.get(
          `SELECT COUNT(*) as success_count 
           FROM referrals 
           WHERE referrer_id = ? AND status = 'completed'`,
          [userId],
          (err, row) => {
            if (err) reject(err);
            else resolve(row);
          }
        );
      });

      return {
        balance: user?.referral_balance || 0,
        referral_code: user?.referral_code,
        total_clicks: clicksResult?.total_clicks || 0,
        pending_referrals: pendingResult?.pending_count || 0,
        successful_referrals: successResult?.success_count || 0
      };
    } catch (error) {
      console.error('Error getting user referral stats:', error);
      throw error;
    }
  }

  /**
   * Track referral click
   * @param {string} referralCode - Referral code
   * @param {string} ipAddress - IP address
   * @param {string} userAgent - User agent
   * @returns {Promise<void>}
   */
  static async trackClick(referralCode, ipAddress, userAgent) {
    try {
      const clickId = uuidv4();
      await new Promise((resolve, reject) => {
        db.run(
          `INSERT INTO referral_clicks (id, referral_code, ip_address, user_agent) 
           VALUES (?, ?, ?, ?)`,
          [clickId, referralCode, ipAddress, userAgent],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });
    } catch (error) {
      console.error('Error tracking referral click:', error);
      throw error;
    }
  }

  /**
   * Process referral signup
   * @param {string} referralCode - Referral code
   * @param {string} newUserId - New user ID
   * @returns {Promise<void>}
   */
  static async processReferralSignup(referralCode, newUserId) {
    try {
      // Update referral record with referee
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE referrals 
           SET referee_id = ?, status = 'signed_up' 
           WHERE referral_code = ? AND referee_id IS NULL`,
          [newUserId, referralCode],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      // Update user's referred_by field
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE users SET referred_by = ? WHERE id = ?`,
          [referralCode, newUserId],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });
    } catch (error) {
      console.error('Error processing referral signup:', error);
      throw error;
    }
  }

  /**
   * Calculate commission based on plan price
   * @param {number} planPrice - Plan price in IDR
   * @returns {number} Commission amount (5% of plan price)
   */
  static calculateCommission(planPrice) {
    return Math.floor(planPrice * 0.05); // 5% commission
  }

  /**
   * Process referral commission when payment is successful
   * @param {string} transactionId - Transaction ID
   * @param {string} userId - User who made payment (referee)
   * @param {number} paidAmount - Amount paid
   * @returns {Promise<void>}
   */
  static async processCommission(transactionId, userId, paidAmount) {
    try {
      // Check if user was referred
      const user = await new Promise((resolve, reject) => {
        db.get(
          `SELECT referred_by FROM users WHERE id = ?`,
          [userId],
          (err, row) => {
            if (err) reject(err);
            else resolve(row);
          }
        );
      });

      if (!user?.referred_by) {
        return; // User was not referred
      }

      // Get referral record
      const referral = await this.getReferralByCode(user.referred_by);
      if (!referral) {
        console.error('Referral code not found:', user.referred_by);
        return;
      }

      // Calculate commission (5% of paid amount)
      const commissionAmount = this.calculateCommission(paidAmount);

      // Create earning record
      const earningId = uuidv4();
      await new Promise((resolve, reject) => {
        db.run(
          `INSERT INTO referral_earnings (id, user_id, referral_id, transaction_id, amount, status) 
           VALUES (?, ?, ?, ?, ?, 'completed')`,
          [earningId, referral.referrer_id, referral.id, transactionId, commissionAmount],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      // Update referrer's balance
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE users 
           SET referral_balance = referral_balance + ? 
           WHERE id = ?`,
          [commissionAmount, referral.referrer_id],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      // Update referral status to completed
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE referrals 
           SET status = 'completed', commission_amount = ?, commission_paid = 1, completed_at = CURRENT_TIMESTAMP 
           WHERE id = ?`,
          [commissionAmount, referral.id],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      console.log(`✅ Referral commission processed: ${commissionAmount} IDR for user ${referral.referrer_id}`);
    } catch (error) {
      console.error('Error processing referral commission:', error);
      throw error;
    }
  }

  /**
   * Get user's referral earnings history
   * @param {string} userId - User ID
   * @param {number} limit - Limit results
   * @param {number} offset - Offset for pagination
   * @returns {Promise<Array>} Earnings history
   */
  static getUserEarnings(userId, limit = 20, offset = 0) {
    return new Promise((resolve, reject) => {
      db.all(
        `SELECT re.*, r.referral_code, u.username as referee_username, t.order_id
         FROM referral_earnings re
         JOIN referrals r ON re.referral_id = r.id
         LEFT JOIN users u ON r.referee_id = u.id
         LEFT JOIN transactions t ON re.transaction_id = t.id
         WHERE re.user_id = ?
         ORDER BY re.created_at DESC
         LIMIT ? OFFSET ?`,
        [userId, limit, offset],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows || []);
        }
      );
    });
  }

  /**
   * Create withdrawal request
   * @param {Object} withdrawalData - Withdrawal request data
   * @returns {Promise<Object>} Created withdrawal request
   */
  static async createWithdrawalRequest(withdrawalData) {
    try {
      const { userId, amount, bankName, accountNumber, accountName } = withdrawalData;
      
      // Check if user has sufficient balance
      const user = await new Promise((resolve, reject) => {
        db.get(
          `SELECT referral_balance FROM users WHERE id = ?`,
          [userId],
          (err, row) => {
            if (err) reject(err);
            else resolve(row);
          }
        );
      });

      if (!user || user.referral_balance < amount) {
        throw new Error('Insufficient balance');
      }

      if (amount < 50000) {
        throw new Error('Minimum withdrawal amount is Rp 50,000');
      }

      // Create withdrawal request
      const requestId = uuidv4();
      await new Promise((resolve, reject) => {
        db.run(
          `INSERT INTO withdrawal_requests (id, user_id, amount, bank_name, account_number, account_name) 
           VALUES (?, ?, ?, ?, ?, ?)`,
          [requestId, userId, amount, bankName, accountNumber, accountName],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      // Deduct amount from user balance (pending withdrawal)
      await new Promise((resolve, reject) => {
        db.run(
          `UPDATE users SET referral_balance = referral_balance - ? WHERE id = ?`,
          [amount, userId],
          function(err) {
            if (err) reject(err);
            else resolve();
          }
        );
      });

      return { id: requestId, ...withdrawalData };
    } catch (error) {
      console.error('Error creating withdrawal request:', error);
      throw error;
    }
  }
}

module.exports = Referral;
